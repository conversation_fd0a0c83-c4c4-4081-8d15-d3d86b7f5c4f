{"name": "n8n-migration-tool", "version": "1.0.0", "private": true, "description": "N8N Workflow Migration Tool", "scripts": {"migrate": "ts-node migrate.ts", "migrate:dry": "ts-node migrate.ts --dry-run", "setup": "npm install && chmod +x migrate.ts"}, "dependencies": {"axios": "^1.6.0", "@aws-sdk/client-ssm": "^3.450.0", "@aws-sdk/client-sts": "^3.450.0"}, "devDependencies": {"@types/node": "^20.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}