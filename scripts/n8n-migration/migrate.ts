#!/usr/bin/env ts-node

import * as path from 'path';
import axios from 'axios';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';

// CLI Interface
async function main() {
  const args = process.argv.slice(2);

  if (args.length < 3) {
    console.log(`
Usage: ./migrate.ts <source> <target1,target2,...> <tag1,tag2,...> [options]

Options:
  --dry-run     : Preview what would be migrated without making changes
  --overwrite   : Overwrite existing workflows with same name

Requirements:
  - AWS CLI configured with 'chainmatic' profile
  - MFA authentication (use awsume, aws-vault, or manual AWS CLI)
  - API tokens stored in AWS Parameter Store at /customers/{customer-id}/n8n/api-key

Examples:
  # Single destination
  ./migrate.ts dr-pfoten staging-client "ad-winners"

  # Multiple destinations
  ./migrate.ts dr-pfoten staging-client,production-client,backup-client "ad-winners,production"

  # With options
  ./migrate.ts dr-pfoten staging-client,production-client "core" --dry-run
  ./migrate.ts source target1,target2 "backup" --overwrite

Note: If MFA is required, the script will exit with instructions
      Workflows are exported once from source and imported to all destinations
`);
    process.exit(1);
  }

  const [sourceCustomer, targetCustomersStr, tagsStr] = args;
  const targetCustomers = targetCustomersStr
    .split(',')
    .map((target) => target.trim());
  const tags = tagsStr.split(',').map((tag) => tag.trim());

  const options: MigrationOptions = {
    sourceCustomer,
    targetCustomers,
    tags,
    dryRun: args.includes('--dry-run'),
    overwrite: args.includes('--overwrite'),
  };

  const migrationTool = new N8NMigrationTool();
  await migrationTool.migrate(options);
}

interface CustomerConfig {
  id: string;
  name: string;
  n8nBaseUrl: string;
  apiToken?: string;
}

interface WorkflowData {
  id: string;
  name: string;
  nodes: any[];
  connections: any;
  tags: string[];
  active: boolean;
  settings?: any;
  staticData?: any;
}

interface MigrationOptions {
  sourceCustomer: string;
  targetCustomers: string[];
  tags: string[];
  dryRun?: boolean;
  overwrite?: boolean;
}

class N8NMigrationTool {
  private customersDir = '../../customers';
  private ssmClient: SSMClient;

  constructor() {
    // Don't set AWS_PROFILE - rely on environment variables or awsume
    if (!process.env.AWS_REGION) {
      process.env.AWS_REGION = 'eu-west-2';
    }

    this.ssmClient = new SSMClient({
      region: process.env.AWS_REGION || 'eu-west-2',
    });
  }

  async getApiTokenFromParameterStore(
    customerId: string,
  ): Promise<string | null> {
    const parameterName = `/customers/${customerId}/n8n/api-key`;

    try {
      const command = new GetParameterCommand({
        Name: parameterName,
        WithDecryption: true, // For SecureString parameters
      });

      const response = await this.ssmClient.send(command);
      return response.Parameter?.Value || null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('multi-factor authentication')) {
        console.error(`\n❌ AWS MFA Required for ${customerId}`);
        console.error(
          'Please authenticate with MFA first using one of these tools:',
        );
        console.error('');
        console.error('Option 1 - awsume:');
        console.error('  awsume chainmatic');
        console.error('');
        console.error('Option 2 - aws-vault:');
        console.error('  aws-vault exec chainmatic -- ./migrate.ts ...');
        console.error('');
        console.error('Option 3 - AWS CLI:');
        console.error(
          '  aws sts get-session-token --profile chainmatic --serial-number <mfa-arn> --token-code <code>',
        );
        console.error('');
        process.exit(1);
      } else {
        console.warn(
          `⚠️  Could not fetch API token from Parameter Store for ${customerId}: ${errorMessage}`,
        );
      }
      return null;
    }
  }

  async loadCustomerConfig(customerId: string): Promise<CustomerConfig> {
    try {
      // Import the customer config directly using dynamic import
      const configPath = path.resolve(
        this.customersDir,
        customerId,
        'customer.config.ts',
      );
      const { customerConfig } = await import(configPath);

      // Get API token from Parameter Store (with MFA prompt if needed)
      const apiToken = await this.getApiTokenFromParameterStore(customerId);

      if (!apiToken) {
        throw new Error(`No API token found for ${customerId}`);
      }

      return {
        ...customerConfig,
        apiToken,
      };
    } catch (error) {
      throw new Error(
        `Failed to load customer config for ${customerId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async fetchWorkflowsByTag(
    config: CustomerConfig,
    tag: string,
  ): Promise<WorkflowData[]> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows?tags=${encodeURIComponent(tag)}`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['Authorization'] = `Bearer ${config.apiToken}`;
    }

    try {
      console.log(`📡 Fetching workflows with tag: ${tag}`);
      const response = await axios.get(url, { headers });
      const workflows = response.data.data || response.data;
      console.log(`Found ${workflows.length} workflows with tag "${tag}"`);
      return workflows;
    } catch (error) {
      throw new Error(
        `Failed to fetch workflows with tag "${tag}" from ${config.n8nBaseUrl}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async fetchWorkflows(config: CustomerConfig): Promise<WorkflowData[]> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['Authorization'] = `Bearer ${config.apiToken}`;
    }

    try {
      const response = await axios.get(url, { headers });
      return response.data.data || response.data;
    } catch (error) {
      throw new Error(
        `Failed to fetch workflows from ${config.n8nBaseUrl}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  // Note: This method is kept for potential future use, but not needed for migration
  // since fetchWorkflowsByTag already returns full workflow data
  async exportWorkflow(
    config: CustomerConfig,
    workflowId: string,
  ): Promise<WorkflowData> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows/${workflowId}`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['Authorization'] = `Bearer ${config.apiToken}`;
    }

    try {
      const response = await axios.get(url, { headers });
      return response.data;
    } catch (error) {
      throw new Error(
        `Failed to export workflow ${workflowId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async importWorkflow(
    config: CustomerConfig,
    workflow: WorkflowData,
    overwrite = false,
  ): Promise<void> {
    const baseUrl = `${config.n8nBaseUrl}/api/v1/workflows`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['Authorization'] = `Bearer ${config.apiToken}`;
    }

    // Remove ID for import (let target assign new ID)
    const { id, ...workflowData } = workflow;

    try {
      if (overwrite) {
        // Try to find existing workflow by name and update
        const existing = await this.findWorkflowByName(config, workflow.name);
        if (existing) {
          await axios.put(`${baseUrl}/${existing.id}`, workflowData, {
            headers,
          });
          console.log(`✅ Updated workflow: ${workflow.name}`);
          return;
        }
      }

      // Create new workflow
      await axios.post(baseUrl, workflowData, { headers });
      console.log(`✅ Created workflow: ${workflow.name}`);
    } catch (error) {
      throw new Error(
        `Failed to import workflow ${workflow.name}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async findWorkflowByName(
    config: CustomerConfig,
    name: string,
  ): Promise<WorkflowData | null> {
    const workflows = await this.fetchWorkflows(config);
    return workflows.find((w) => w.name === name) || null;
  }

  filterWorkflowsByTags(
    workflows: WorkflowData[],
    tags: string[],
  ): WorkflowData[] {
    // This method is now deprecated since we fetch by tag directly
    // Kept for backward compatibility if needed
    return workflows.filter((workflow) => {
      if (!workflow.tags || workflow.tags.length === 0) return false;
      return tags.some((tag) => workflow.tags.includes(tag));
    });
  }

  async fetchAllWorkflowsByTags(
    config: CustomerConfig,
    tags: string[],
  ): Promise<WorkflowData[]> {
    const allWorkflows = new Map<string, WorkflowData>();

    // Fetch workflows for each tag separately
    for (const tag of tags) {
      try {
        const workflows = await this.fetchWorkflowsByTag(config, tag);

        // Add to map to avoid duplicates (workflow can have multiple tags)
        workflows.forEach((workflow) => {
          allWorkflows.set(workflow.id, workflow);
        });
      } catch (error) {
        console.error(
          `❌ Failed to fetch workflows for tag "${tag}": ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    return Array.from(allWorkflows.values());
  }

  async migrate(options: MigrationOptions): Promise<void> {
    console.log(
      `🚀 Starting migration from ${options.sourceCustomer} to [${options.targetCustomers.join(', ')}]`,
    );
    console.log(`📋 Tags: ${options.tags.join(', ')}`);

    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made');
    }

    try {
      // Load source config
      const sourceConfig = await this.loadCustomerConfig(
        options.sourceCustomer,
      );
      console.log(`📡 Source: ${sourceConfig.n8nBaseUrl}`);

      // Load all target configs
      const targetConfigs = new Map<string, CustomerConfig>();
      console.log('\n🎯 Targets:');

      for (const targetCustomer of options.targetCustomers) {
        try {
          const targetConfig = await this.loadCustomerConfig(targetCustomer);
          targetConfigs.set(targetCustomer, targetConfig);
          console.log(`   ${targetCustomer}: ${targetConfig.n8nBaseUrl}`);
        } catch (error) {
          console.error(
            `❌ Failed to load config for ${targetCustomer}: ${error instanceof Error ? error.message : String(error)}`,
          );
          console.log('Continuing with other targets...');
        }
      }

      if (targetConfigs.size === 0) {
        console.error('❌ No valid target configurations found');
        return;
      }

      // Fetch workflows from source using individual tag requests (OR logic)
      console.log('\n📥 Fetching workflows from source...');
      const workflows = await this.fetchAllWorkflowsByTags(
        sourceConfig,
        options.tags,
      );

      if (workflows.length === 0) {
        console.log('❌ No workflows found with the specified tags');
        return;
      }

      // List workflows to be migrated
      console.log('\n📋 Workflows to migrate:');
      workflows.forEach((workflow, index) => {
        console.log(
          `${index + 1}. ${workflow.name} (tags: ${workflow.tags?.join(', ') || 'none'})`,
        );
      });

      console.log(
        `\n📊 Will migrate ${workflows.length} workflow(s) to ${targetConfigs.size} destination(s)`,
      );

      if (options.dryRun) {
        console.log('\n✅ Dry run completed - no changes made');
        return;
      }

      // Workflows are already fully loaded from the tag API, no need to export them again
      console.log('\n✅ Workflows already loaded with full data from source');
      const exportedWorkflows = workflows;

      // Import to each destination
      console.log('\n📥 Importing to destinations...');
      const results = new Map<
        string,
        { success: number; failed: number; errors: string[] }
      >();

      for (const [targetCustomer, targetConfig] of Array.from(
        targetConfigs.entries(),
      )) {
        console.log(`\n🎯 Importing to ${targetCustomer}...`);

        const result = { success: 0, failed: 0, errors: [] as string[] };

        for (const workflow of exportedWorkflows) {
          try {
            console.log(`  📥 Importing: ${workflow.name}`);
            await this.importWorkflow(
              targetConfig,
              workflow,
              options.overwrite,
            );
            result.success++;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            console.error(
              `  ❌ Failed to import ${workflow.name}: ${errorMessage}`,
            );
            result.failed++;
            result.errors.push(`${workflow.name}: ${errorMessage}`);
          }
        }

        results.set(targetCustomer, result);
      }

      // Summary report
      console.log('\n📊 Migration Summary:');
      for (const [targetCustomer, result] of Array.from(results.entries())) {
        console.log(`\n  ${targetCustomer}:`);
        console.log(`    ✅ Success: ${result.success}`);
        console.log(`    ❌ Failed: ${result.failed}`);

        if (result.errors.length > 0) {
          console.log(`    Errors:`);
          result.errors.forEach((error) => console.log(`      - ${error}`));
        }
      }

      const totalSuccess = Array.from(results.values()).reduce(
        (sum, r) => sum + r.success,
        0,
      );
      const totalFailed = Array.from(results.values()).reduce(
        (sum, r) => sum + r.failed,
        0,
      );

      console.log(`\n🎉 Migration completed!`);
      console.log(
        `📊 Total: ${totalSuccess} successful, ${totalFailed} failed across ${targetConfigs.size} destination(s)`,
      );
    } catch (error) {
      console.error(
        `❌ Migration failed: ${error instanceof Error ? error.message : String(error)}`,
      );
      process.exit(1);
    }
  }
}

export { N8NMigrationTool, MigrationOptions };

// Execute main function if this file is run directly
if (require.main === module) {
  main().catch(console.error);
}
