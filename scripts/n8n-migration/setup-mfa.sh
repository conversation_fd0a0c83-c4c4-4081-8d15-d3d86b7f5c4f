#!/bin/bash

# AWS MFA Setup Helper for N8N Migration Tool
# This script helps you set up temporary AWS credentials with MFA

echo "🔐 AWS MFA Setup Helper"
echo "======================="

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if chainmatic profile exists
if ! aws configure list-profiles | grep -q "chainmatic"; then
    echo "❌ AWS profile 'chainmatic' not found."
    echo "Please set up your AWS profile first:"
    echo "  aws configure --profile chainmatic"
    exit 1
fi

# Get MFA device ARN
echo "📱 Please enter your MFA device ARN:"
echo "   (e.g., arn:aws:iam::123456789012:mfa/your-username)"
read -p "MFA ARN: " MFA_ARN

if [ -z "$MFA_ARN" ]; then
    echo "❌ MFA ARN is required"
    exit 1
fi

# Get MFA token
read -p "🔢 Enter your current MFA token: " MFA_TOKEN

if [ -z "$MFA_TOKEN" ]; then
    echo "❌ MFA token is required"
    exit 1
fi

echo "🔄 Getting temporary credentials..."

# Get session token
CREDENTIALS=$(aws sts get-session-token \
    --profile chainmatic \
    --serial-number "$MFA_ARN" \
    --token-code "$MFA_TOKEN" \
    --duration-seconds 3600 \
    --output json)

if [ $? -ne 0 ]; then
    echo "❌ Failed to get session token"
    exit 1
fi

# Extract credentials
ACCESS_KEY=$(echo "$CREDENTIALS" | jq -r '.Credentials.AccessKeyId')
SECRET_KEY=$(echo "$CREDENTIALS" | jq -r '.Credentials.SecretAccessKey')
SESSION_TOKEN=$(echo "$CREDENTIALS" | jq -r '.Credentials.SessionToken')

# Set up temporary profile
echo "🔧 Setting up temporary profile 'chainmatic-mfa'..."

aws configure set aws_access_key_id "$ACCESS_KEY" --profile chainmatic-mfa
aws configure set aws_secret_access_key "$SECRET_KEY" --profile chainmatic-mfa
aws configure set aws_session_token "$SESSION_TOKEN" --profile chainmatic-mfa
aws configure set region eu-west-2 --profile chainmatic-mfa

echo "✅ Temporary credentials set up successfully!"
echo ""
echo "🚀 Now you can run the migration tool with:"
echo "   AWS_PROFILE=chainmatic-mfa ./migrate.ts dr-pfoten chainmatic \"ad-winners\" --dry-run"
echo ""
echo "⏰ These credentials will expire in 1 hour."
